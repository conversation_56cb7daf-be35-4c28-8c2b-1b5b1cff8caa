#!/usr/bin/env python3
"""
Test script to verify date parsing logic for events happening today.
"""

from datetime import datetime, date
from database import is_event_happening_on_date, get_events_happening_today

def test_date_parsing():
    """Test the date parsing function with various date formats."""
    
    # Test date - let's use July 13, 2025 as "today"
    test_date = date(2025, 7, 13)
    
    test_cases = [
        # Format: (date_string, expected_result, description)
        ("July 11-13, 2025", True, "Multi-day event ending today"),
        ("July 13, 2025", True, "Single day event today"),
        ("July 13-15, 2025", True, "Multi-day event starting today"),
        ("July 10-12, 2025", False, "Event ended yesterday"),
        ("July 14-16, 2025", False, "Event starts tomorrow"),
        ("July 11-15, 2025", True, "Multi-day event spanning today"),
        ("June 30 - July 15, 2025", True, "Cross-month event spanning today"),
        ("June 30 - July 12, 2025", False, "Cross-month event ended yesterday"),
        ("July 14 - August 1, 2025", False, "Cross-month event starts tomorrow"),
    ]
    
    print(f"Testing date parsing for target date: {test_date}")
    print("=" * 60)
    
    for date_str, expected, description in test_cases:
        result = is_event_happening_on_date(date_str, test_date)
        status = "✓ PASS" if result == expected else "✗ FAIL"
        print(f"{status} | {date_str:<25} | {description}")
        if result != expected:
            print(f"      Expected: {expected}, Got: {result}")
    
    print("\n" + "=" * 60)

def test_get_events_today():
    """Test getting events happening today from the database."""
    print("Testing get_events_happening_today function...")

    # First, let's see what today's date is
    today = datetime.now().date()
    print(f"Today's date: {today}")

    try:
        # Let's also check what events are in the database
        from database import get_upcoming_events
        all_events = get_upcoming_events()
        print(f"Total upcoming events in database: {len(all_events)}")

        if all_events:
            print("Sample events in database:")
            for event in all_events[:5]:  # Show first 5 events
                start_date, name, dates, location, source, country, province_state, url = event
                print(f"  - {name}: {dates} (starts: {start_date.date()})")

        print("\nTesting events happening today:")
        # Test with different day ranges
        for days_range in [0, 1, 2]:
            events = get_events_happening_today(days_range=days_range)
            print(f"Days range {days_range}: Found {len(events)} events")

            for event in events[:3]:  # Show first 3 events
                start_date, name, dates, location, source, country, province_state, url = event
                print(f"  - {name}: {dates}")

        # Let's also test for a specific date where we know there should be events
        print(f"\nTesting for July 15, 2025 (should find ConnectiCon and Tekko):")
        test_date_july_15 = date(2025, 7, 15)

        # Test the date parsing function directly
        test_events = [
            ("July 17-20, 2025", "ConnectiCon 2025"),
            ("July 17-20, 2025", "Tekko 2025"),
            ("July 14-18, 2025", "Camp Innovate")
        ]

        for date_str, event_name in test_events:
            result = is_event_happening_on_date(date_str, test_date_july_15)
            print(f"  {event_name} ({date_str}): {'✓' if result else '✗'} happening on July 15")

        # Test for July 18 (should find Camp Innovate and multi-day events)
        print(f"\nTesting for July 18, 2025:")
        test_date_july_18 = date(2025, 7, 18)

        for date_str, event_name in test_events:
            result = is_event_happening_on_date(date_str, test_date_july_18)
            print(f"  {event_name} ({date_str}): {'✓' if result else '✗'} happening on July 18")

    except Exception as e:
        print(f"Error testing database function: {e}")
        import traceback
        traceback.print_exc()

def test_specific_date_functionality():
    """Test the functionality by checking events for specific dates where we know there should be matches."""
    print("\n" + "=" * 60)
    print("Testing functionality for specific dates with known events")
    print("=" * 60)

    try:
        from database import get_upcoming_events
        all_events = get_upcoming_events()

        # Test for July 18, 2025 - should find Camp Innovate, ConnectiCon, and Tekko
        test_date = date(2025, 7, 18)
        print(f"Testing events happening on {test_date}:")

        matching_events = []
        for event in all_events:
            start_date, name, dates, location, source, country, province_state, url = event
            if is_event_happening_on_date(dates, test_date):
                matching_events.append((name, dates))

        print(f"Found {len(matching_events)} events happening on {test_date}:")
        for name, dates in matching_events[:10]:  # Show first 10
            print(f"  - {name}: {dates}")

        # Test for July 17, 2025 - should find ConnectiCon and Tekko starting
        test_date = date(2025, 7, 17)
        print(f"\nTesting events happening on {test_date}:")

        matching_events = []
        for event in all_events:
            start_date, name, dates, location, source, country, province_state, url = event
            if is_event_happening_on_date(dates, test_date):
                matching_events.append((name, dates))

        print(f"Found {len(matching_events)} events happening on {test_date}:")
        for name, dates in matching_events[:10]:  # Show first 10
            print(f"  - {name}: {dates}")

    except Exception as e:
        print(f"Error in specific date test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Date Parsing Test Script")
    print("=" * 60)

    test_date_parsing()
    print()
    test_get_events_today()
    test_with_mock_today()
