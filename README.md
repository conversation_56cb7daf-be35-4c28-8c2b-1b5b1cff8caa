# Canadian & US Anime Conventions Tracker

A web application that displays upcoming Canadian and US anime conventions by scraping data from multiple sources and storing it in a SQLite database.

## Features

- **Automated Data Collection**: Scrapes convention data from AnimeCons.ca, AnimeCons.com, and Eventbrite
- **Multi-Country Support**: Tracks conventions in both Canada and the United States
- **Advanced Filtering**: Filter by country, province (Canada), and state (US)
- **Database Storage**: Uses SQLite to store event data locally
- **Background Processing**: Scraper runs independently from the web application
- **Web Interface**: Clean, responsive Bootstrap-based UI with interactive filters
- **API Endpoints**: JSON API for programmatic access
- **Cron Job Ready**: Designed to run scraper via cron for automated updates
- **User Submissions**: Users can submit new conventions for admin approval
- **Admin Panel**: Hidden admin interface for approving user submissions and managing the system

## Architecture

The application is split into two main components:

1. **Scraper** (`scraper.py`): Standalone script that fetches data and saves to database
2. **Web App** (`app.py`): Flask application that serves data from the database

### Database Structure

- **Main Database** (`anime_conventions.db`): Stores scraped convention data
- **User Database** (`user_conventions.db`): Stores user submissions and admin credentials

## Setup

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd Animecons
pip install -r requirements.txt
```

### 2. Initialize Databases

```bash
python database.py
```

Or run the scraper with the init flag:

```bash
python scraper.py --init-db
```

### 3. Run Initial Scrape

```bash
python scraper.py
```

### 4. Start Web Application

```bash
python app.py
```

Visit `http://localhost:5000` to view the conventions.

## Usage

### Scraper Commands

```bash
# Run scraper (default)
python scraper.py

# Verbose output
python scraper.py --verbose

# Quiet mode (errors only)
python scraper.py --quiet

# Show database statistics
python scraper.py --stats

# Clear old/past events
python scraper.py --clear-old

# Initialize database only
python scraper.py --init-db
```

### Web Application

The Flask app provides:

- **Main Page** (`/`): HTML table of upcoming conventions with filtering and user submission form
- **Events API** (`/api/events`): JSON list of events with optional filtering
- **Stats API** (`/api/stats`): Database statistics
- **Filters API** (`/api/filters`): Available filter options

### User Submissions

Users can submit new conventions through the form on the main page. Submissions require:

- Event Name
- Dates (e.g., "July 1-4, 2025")
- Location
- Country (Canada or United States)
- Province/State
- URL (optional)

Submissions are stored in the user database and require admin approval before appearing on the main page.

### Admin Panel

Access the admin panel at `/admin` with default credentials:
- **Username**: `admin`
- **Password**: `admin`

**Important**: Change the default password immediately after first login!

The admin panel provides:
- **Pending Approvals**: Review and approve user-submitted conventions
- **Password Management**: Change admin password
- **Dashboard**: Overview of pending submissions

### Filtering

The web interface supports filtering by:

- **Country**: Canada (CA) or United States (US)
- **Province**: Canadian provinces (when Canada is selected)
- **State**: US states (when United States is selected)

Filters can be combined and are applied via URL parameters.

### API Examples

```bash
# Get all events as JSON
curl http://localhost:5000/api/events

# Get events filtered by country
curl "http://localhost:5000/api/events?country=CA"

# Get events filtered by province/state
curl "http://localhost:5000/api/events?province_state=ON"

# Get database statistics
curl http://localhost:5000/api/stats

# Get available filter options
curl http://localhost:5000/api/filters
```

## Automated Updates with Cron

To automatically update the convention data, set up a cron job:

```bash
# Edit crontab
crontab -e

# Add entry to run scraper daily at 6 AM
0 6 * * * cd /path/to/Animecons && python scraper.py --quiet

# Or run twice daily (6 AM and 6 PM)
0 6,18 * * * cd /path/to/Animecons && python scraper.py --quiet
```

### Windows Task Scheduler

For Windows users, create a scheduled task:

1. Open Task Scheduler
2. Create Basic Task
3. Set trigger (e.g., daily at 6:00 AM)
4. Set action to start a program:
   - Program: `python`
   - Arguments: `scraper.py --quiet`
   - Start in: `C:\path\to\Animecons`

## Database Schema

### Main Database (`anime_conventions.db`)

```sql
CREATE TABLE conventions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    dates TEXT NOT NULL,
    location TEXT NOT NULL,
    source TEXT NOT NULL,
    start_date TEXT NOT NULL,
    country TEXT NOT NULL,
    province_state TEXT NOT NULL,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    url TEXT,
    UNIQUE(name, start_date, source)
);
```

### User Database (`user_conventions.db`)

```sql
-- User-submitted conventions
CREATE TABLE user_conventions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    dates TEXT NOT NULL,
    location TEXT NOT NULL,
    source TEXT DEFAULT 'User Submission',
    start_date TEXT NOT NULL,
    country TEXT NOT NULL,
    province_state TEXT NOT NULL,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    url TEXT,
    is_approved BOOLEAN DEFAULT 0,
    UNIQUE(name, start_date)
);

-- Admin users
CREATE TABLE admin_users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL
);
```

## Data Sources

- **AnimeCons.ca**: Primary source for Canadian anime conventions
- **AnimeCons.com**: Source for US anime conventions
- **Eventbrite**: Additional events from ticket sales platform (both CA and US)
- **User Submissions**: Community-contributed conventions (approved by admin)

## Configuration

### Security Considerations

1. **Change Default Admin Password**: The default admin password is `admin`. Change it immediately after first login.
2. **Secret Key**: Update the Flask secret key in `app.py` for production use.
3. **Admin URL**: The admin panel is accessible at `/admin`. Consider using a more obscure URL in production.

### Adding New Sources

To add a new data source:

1. Create a new scraper function in `scraper.py`
2. Add it to the `scrapers` list in `aggregate_events()`
3. Follow the existing pattern returning `List[Event]`

### Customizing Scraping

Edit `scraper.py` to:
- Modify scraping intervals
- Add new data sources
- Change deduplication logic
- Adjust date parsing

## Troubleshooting

### Common Issues

1. **Database locked**: Ensure scraper isn't running when accessing web app
2. **No events showing**: Run scraper first to populate database
3. **Scraper failing**: Check network connectivity and site availability
4. **Filters not working**: Check that the database has been reinitialized with the new schema
5. **Admin login not working**: Ensure both databases are initialized with `python database.py`

### Debugging

Enable verbose logging:

```bash
python scraper.py --verbose
```

Check database contents:

```bash
python scraper.py --stats
```

### Log Files

For production deployments, consider redirecting scraper output to log files:

```bash
# In crontab
0 6 * * * cd /path/to/Animecons && python scraper.py --quiet >> scraper.log 2>&1
```

## Development

### Project Structure

```
Animecons/
├── app.py              # Flask web application
├── scraper.py          # Data scraping script
├── database.py         # Database operations
├── requirements.txt    # Python dependencies
├── templates/
│   ├── index.html      # Web interface template
│   ├── admin_login.html # Admin login page
│   └── admin_dashboard.html # Admin dashboard
├── anime_conventions.db # Main SQLite database
└── user_conventions.db # User submissions database
```

### Adding Features

1. **New scrapers**: Add functions to `scraper.py`
2. **Web features**: Modify `app.py` and templates
3. **Database changes**: Update `database.py` schema
4. **Admin features**: Extend admin dashboard functionality

## License

This project is for educational and personal use. Please respect the terms of service of the websites being scraped.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review log files for errors
3. Ensure all dependencies are installed
4. Verify network connectivity to source websites 