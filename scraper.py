#!/usr/bin/env python3
"""
Standalone scraper for Canadian and US anime conventions.
This script scrapes data from various sources and saves it to a SQLite database.
Can be run manually or via cron job.
"""

import requests
from bs4 import BeautifulSoup
from datetime import datetime
import re
import logging
import sys
import argparse
from typing import List, Tu<PERSON>, Dict
import os
import json

from database import init_database, save_events, clear_old_events, get_database_stats

# Reusable HTTP headers (helps bypass some basic bot blocks)
HEADERS = {
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/124.0 Safari/537.36"
    )
}

# Updated Event type to include country, province/state, and url
Event = Tuple[datetime, str, str, str, str, str, str, str]  # (start, name, dates, location, source, country, province_state, url)

US_STATE_ABBR_TO_NAME = {
    "AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "FL": "Florida", "GA": "Georgia", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PA": "Pennsylvania", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming"
}

CA_PROVINCE_ABBR_TO_NAME = {
    "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon"
}


def parse_event_row(cells):
    """Extracts (name, dates, location) from a <tr> row."""
    if len(cells) < 3:
        return None
    name = cells[0].get_text(strip=True)
    dates = cells[1].get_text(" ", strip=True)
    location = cells[2].get_text(" ", strip=True)
    return name, dates, location


def first_event_date(dates_str):
    """Return a datetime object representing the first day mentioned in dates_str.
    Falls back to 12/31 of the detected year if parsing fails."""
    # Look for a 4-digit year
    m = re.search(r"(20\d{2})", dates_str)
    if not m:
        return None
    year = int(m.group(1))

    # Extract the first month and day (e.g. "January 17" from "January 17-19, 2025")
    try:
        month_day = dates_str.split(",")[0].strip()  # "January 17-19"
        month, day_part = month_day.split(" ", 1)
        day = re.split("[-–]", day_part)[0]  # handle ranges "17-19"
        first_date = datetime.strptime(f"{month} {day} {year}", "%B %d %Y")
        return first_date
    except Exception:
        # Fallback: assume event ends on Dec 31 of the year
        return datetime(year, 12, 31)


# --------------------------- AnimeCons common helpers --------------------------- #

def extract_convention_url_from_event_page(event_url: str) -> str:
    """Extract the convention's official website URL from an individual event page."""
    try:
        resp = requests.get(event_url, headers=HEADERS, timeout=15)
        resp.raise_for_status()
        soup = BeautifulSoup(resp.text, "html.parser")
        
        # Look for the "Visit Convention Site" button in the box-body div
        box_body = soup.find("div", class_="box-body")
        if box_body:
            # Find the button with "Visit Convention Site" text
            visit_button = box_body.find("button", string=lambda text: text and "Visit Convention Site" in text)
            if visit_button:
                # Get the parent anchor tag
                parent_link = visit_button.find_parent("a")
                if parent_link and parent_link.get("href"):
                    # Extract the clean URL by removing UTM parameters
                    url = parent_link["href"]
                    # Remove UTM parameters if present
                    if "?" in url:
                        base_url = url.split("?")[0]
                        return base_url
                    return url
        
        # Fallback: look for any link that might be the convention site
        for link in box_body.find_all("a", href=True) if box_body else []:
            href = link["href"]
            # Skip internal links and common tracking domains
            if (href.startswith("http") and 
                not href.startswith("https://animecons.com") and
                not href.startswith("https://animecons.ca") and
                not href.startswith("https://www.google.com") and
                not href.startswith("https://media.animecons.com")):
                # Remove UTM parameters
                if "?" in href:
                    base_url = href.split("?")[0]
                    return base_url
                return href
                
    except Exception as exc:
        logging.debug(f"Failed to extract convention URL from {event_url}: {exc}")
    
    return ""


def _fetch_animecons_table(url: str, source_name: str, country: str) -> List[Event]:
    """Generic table parser for AnimeCons/ FanCons schedule pages with enhanced URL extraction."""
    logging.debug("Fetching %s", url)
    resp = requests.get(url, headers=HEADERS, timeout=15)
    resp.raise_for_status()
    soup = BeautifulSoup(resp.text, "html.parser")

    events: List[Event] = []
    for table in soup.find_all("table"):
        header = table.find("tr")
        if not header or "Dates" not in header.get_text() or "Location" not in header.get_text():
            continue
        for row in table.find_all("tr")[1:]:
            cells = row.find_all("td")
            parsed = parse_event_row(cells)
            if not parsed:
                continue
            name, dates, location = parsed
            start = first_event_date(dates)
            if start and start >= datetime.now():
                # Extract province/state from location
                province_state = "N/A"
                if "," in location:
                    province_state = location.split(",")[-1].strip()
                    # Map US state abbreviations to full names
                    if country == "US" and province_state in US_STATE_ABBR_TO_NAME:
                        province_state = US_STATE_ABBR_TO_NAME[province_state]
                    # Map CA province abbreviations to full names
                    if country == "CA" and province_state in CA_PROVINCE_ABBR_TO_NAME:
                        province_state = CA_PROVINCE_ABBR_TO_NAME[province_state]
                
                # Try to find the event page URL to extract convention website
                convention_url = ""
                try:
                    # Look for links in the name cell that might lead to the event page
                    name_cell = cells[0]
                    event_link = name_cell.find("a", href=True)
                    if event_link:
                        event_page_url = event_link["href"]
                        # Make sure it's a relative URL and convert to absolute
                        if event_page_url.startswith("/"):
                            base_domain = "https://animecons.com" if "animecons.com" in url else "https://animecons.ca"
                            event_page_url = base_domain + event_page_url
                        elif not event_page_url.startswith("http"):
                            base_domain = "https://animecons.com" if "animecons.com" in url else "https://animecons.ca"
                            event_page_url = base_domain + "/" + event_page_url
                        
                        # Extract convention URL from the event page
                        convention_url = extract_convention_url_from_event_page(event_page_url)
                        if convention_url:
                            logging.debug(f"Found convention URL for {name}: {convention_url}")
                except Exception as exc:
                    logging.debug(f"Failed to extract convention URL for {name}: {exc}")
                
                events.append((start, name, dates, location, source_name, country, province_state, convention_url))
    return events


def fetch_from_animecons_ca() -> List[Event]:
    """Fetch events from AnimeCons.ca"""
    url = "https://animecons.ca/events/schedule.php?loc=ca"
    return _fetch_animecons_table(url, "AnimeCons.ca", "CA")


def fetch_from_animecons_us() -> List[Event]:
    """Fetch events from AnimeCons.com for the US"""
    url = "https://animecons.com/events/schedule.php?loc=us"
    return _fetch_animecons_table(url, "AnimeCons.com", "US")


def fetch_from_animecons_com() -> List[Event]:
    """Fetch events from AnimeCons.com"""
    url = "https://animecons.com/events/schedule.php?loc=CA"  # CA == Canada on .com site
    return _fetch_animecons_table(url, "AnimeCons.com", "CA")


# --------------------------- Eventbrite scraper --------------------------- #

def fetch_from_eventbrite_jsonld(country: str) -> List[Event]:
    """Fetch Eventbrite events for a given country (CA or US) from the JSON-LD block."""
    if country == "CA":
        urls = [
            "https://www.eventbrite.ca/d/canada/anime/",
            "https://www.eventbrite.ca/d/canada/%23cosplay/"
        ]
        country_code = "CA"
    elif country == "US":
        urls = [
            "https://www.eventbrite.com/d/united-states/anime/",
            "https://www.eventbrite.com/d/united-states/%23cosplay/"
        ]
        country_code = "US"
    else:
        raise ValueError("country must be 'CA' or 'US'")
    
    all_events = []
    for url in urls:
        try:
            resp = requests.get(url, headers=HEADERS, timeout=15)
            resp.raise_for_status()
        except Exception as exc:
            logging.error(f"Eventbrite {country} fetch failed for {url}: {exc}")
            continue
        soup = BeautifulSoup(resp.text, "html.parser")
        script = None
        for s in soup.find_all("script", type="application/ld+json"):
            try:
                data = json.loads(s.string)
                if isinstance(data, dict) and "itemListElement" in data:
                    script = s
                    break
            except Exception:
                continue
        if not script:
            logging.warning(f"No JSON-LD event data found on Eventbrite {country} page: {url}")
            continue
        try:
            data = json.loads(script.string)
            events = []
            for item in data["itemListElement"]:
                evt = item["item"]
                # Parse start and end date
                try:
                    start_dt = datetime.strptime(evt["startDate"], "%Y-%m-%d")
                except Exception:
                    start_dt = datetime(2099, 12, 31)
                end_dt = None
                if evt.get("endDate"):
                    try:
                        end_dt = datetime.strptime(evt["endDate"], "%Y-%m-%d")
                    except Exception:
                        end_dt = None
                # Format dates as 'Month D-D, YYYY' or 'Month D, YYYY'
                if end_dt and start_dt == end_dt:
                    # Single day event (start == end)
                    dates = start_dt.strftime("%B %d, %Y")
                elif end_dt and start_dt.year == end_dt.year and start_dt.month == end_dt.month:
                    # Same month/year: August 8-10, 2025
                    dates = f"{start_dt.strftime('%B')} {start_dt.day}-{end_dt.day}, {start_dt.year}"
                elif end_dt and start_dt.year == end_dt.year:
                    # Same year, different months: August 30-September 1, 2025
                    dates = f"{start_dt.strftime('%B')} {start_dt.day}-{end_dt.strftime('%B')} {end_dt.day}, {start_dt.year}"
                else:
                    # Single day or fallback
                    dates = start_dt.strftime("%B %d, %Y")
                name = evt.get("name", "(No title)")
                location = evt.get("location", {}).get("address", {}).get("streetAddress", "")
                city = evt.get("location", {}).get("address", {}).get("addressLocality", "")
                region = evt.get("location", {}).get("address", {}).get("addressRegion", "")
                province_state = region or "N/A"
                # For US, map abbreviation to full state name
                if country_code == "US" and region in US_STATE_ABBR_TO_NAME:
                    province_state = US_STATE_ABBR_TO_NAME[region]
                # For CA, map abbreviation to full province name
                if country_code == "CA" and region in CA_PROVINCE_ABBR_TO_NAME:
                    province_state = CA_PROVINCE_ABBR_TO_NAME[region]
                full_location = f"{location}, {city}, {province_state}".strip(', ')
                url = evt.get("url", "")
                events.append((start_dt, name, dates, full_location, "Eventbrite", country_code, province_state, url))
            all_events.extend(events)
            logging.info(f"Found {len(events)} events from Eventbrite {country} URL: {url}")
        except Exception as exc:
            logging.error(f"Failed to parse JSON-LD event data for {country} URL {url}: {exc}")
            continue
    
    logging.info(f"Total events from Eventbrite {country}: {len(all_events)}")
    return all_events


def fetch_from_eventbrite() -> List[Event]:
    """Scrapes Eventbrite for upcoming Canadian and US anime conventions using JSON-LD only."""
    ca_events = fetch_from_eventbrite_jsonld("CA")
    us_events = fetch_from_eventbrite_jsonld("US")
    all_events = ca_events + us_events
    logging.info(f"Total Eventbrite events: CA={len(ca_events)}, US={len(us_events)}")
    return all_events


# --------------------------- Aggregator --------------------------- #

def normalize_event_name(name: str) -> str:
    """Normalize event name for better matching by removing common suffixes and normalizing."""
    # Remove common year suffixes and normalize
    normalized = re.sub(r'\s+202[0-9]', '', name.lower())
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    return normalized


def events_are_similar(evt1: Event, evt2: Event) -> bool:
    """Check if two events are likely the same event from different sources."""
    # Check if dates match
    if evt1[0].date() != evt2[0].date():
        return False
    
    # Check if normalized names are similar
    name1 = normalize_event_name(evt1[1])
    name2 = normalize_event_name(evt2[1])
    
    # Exact match after normalization
    if name1 == name2:
        return True
    
    # Check if one name contains the other (e.g., "Louisville Anime-Fest" vs "Louisville Anime-Fest 2025")
    if name1 in name2 or name2 in name1:
        return True
    
    # Check if names are very similar (allowing for minor differences)
    if len(name1) > 10 and len(name2) > 10:
        # If names are long enough, check if they share most words
        words1 = set(name1.split())
        words2 = set(name2.split())
        if len(words1) >= 2 and len(words2) >= 2:
            common_words = words1.intersection(words2)
            if len(common_words) >= min(len(words1), len(words2)) * 0.7:  # 70% word overlap
                return True
    
    return False


def aggregate_events() -> List[Event]:
    """Run all scrapers and merge results, deduplicating by (name, start date) with Eventbrite priority."""
    all_events: List[Event] = []
    scrapers = [
        fetch_from_animecons_ca,
        fetch_from_animecons_us,
        fetch_from_eventbrite,
        # fetch_from_animecons_com,  # Uncomment if needed
    ]
    
    for fetcher in scrapers:
        try:
            logging.info(f"Running scraper: {fetcher.__name__}")
            events = fetcher()
            all_events.extend(events)
            logging.info(f"Scraper {fetcher.__name__} found {len(events)} events")
        except Exception as exc:
            logging.error(f"Scraper {fetcher.__name__} failed: {exc}")

    # Improved deduplication with Eventbrite priority
    unique_events: List[Event] = []
    processed_events = set()
    
    # First pass: collect all events by date for easier comparison
    events_by_date: Dict[datetime.date, List[Event]] = {}
    for evt in all_events:
        date_key = evt[0].date()
        if date_key not in events_by_date:
            events_by_date[date_key] = []
        events_by_date[date_key].append(evt)
    
    # Second pass: deduplicate with priority
    for date_key, events_on_date in events_by_date.items():
        # Sort events by priority (AnimeCons first, then others)
        def priority_key(evt):
            source = evt[4]  # source is the 5th element
            if "AnimeCons" in source:
                return 0  # Highest priority
            return 1  # Lower priority
        
        sorted_events = sorted(events_on_date, key=priority_key)
        
        for evt in sorted_events:
            # Check if this event is similar to any already processed event
            is_duplicate = False
            for existing_evt in unique_events:
                if events_are_similar(evt, existing_evt):
                    # Log which event we're keeping and which we're dropping
                    logging.debug(f"Duplicate detected: keeping '{existing_evt[1]}' ({existing_evt[4]}) over '{evt[1]}' ({evt[4]})")
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_events.append(evt)
    
    # Sort by date
    unique_events = sorted(unique_events, key=lambda e: e[0])
    logging.info(f"Total unique events after deduplication: {len(unique_events)}")
    return unique_events


def main():
    """Main scraper function."""
    parser = argparse.ArgumentParser(description="Scrape Canadian and US anime conventions and save to database")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    parser.add_argument("--quiet", "-q", action="store_true", help="Only log errors")
    parser.add_argument("--init-db", action="store_true", help="Initialize database and exit")
    parser.add_argument("--stats", action="store_true", help="Show database statistics and exit")
    parser.add_argument("--clear-old", action="store_true", help="Clear old events from database")
    
    args = parser.parse_args()
    
    # Configure logging
    if args.quiet:
        log_level = logging.ERROR
    elif args.verbose:
        log_level = logging.DEBUG
    else:
        log_level = logging.INFO
    
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    
    # Delete the database before scraping (unless just initializing, stats, or clearing old)
    if not (args.init_db or args.stats or args.clear_old):
        if os.path.exists(DB_PATH):
            os.remove(DB_PATH)
            logging.info(f"Deleted database file: {DB_PATH}")

    # Initialize database
    try:
        init_database()
    except Exception as exc:
        logging.error(f"Failed to initialize database: {exc}")
        sys.exit(1)
    
    # Handle special commands
    if args.init_db:
        logging.info("Database initialized successfully")
        return
    
    if args.stats:
        stats = get_database_stats()
        print(f"Database Statistics:")
        print(f"  Total events: {stats['total_events']}")
        print(f"  Events by source: {stats['by_source']}")
        print(f"  Last update: {stats['last_update']}")
        return
    
    if args.clear_old:
        clear_old_events()
        return
    
    # Run the scraper
    logging.info("Starting scraper run...")
    start_time = datetime.now()
    
    try:
        # Aggregate events from all sources
        events = aggregate_events()
        
        if not events:
            logging.warning("No events found from any source")
            return
        
        # Save to database
        save_events(events)

        # Debug: print number of events in database after saving
        stats = get_database_stats()
        print(f"[DEBUG] Events in database after scraping: {stats['total_events']}")
        
        # Show summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        logging.info(f"Scraper completed successfully in {duration.total_seconds():.1f} seconds")
        logging.info(f"Saved {len(events)} events to database")
        
        # Show breakdown by source and country
        source_counts = {}
        country_counts = {}
        for event in events:
            source = event[4]  # source is the 5th element
            country = event[5]  # country is the 6th element
            source_counts[source] = source_counts.get(source, 0) + 1
            country_counts[country] = country_counts.get(country, 0) + 1
        
        for source, count in source_counts.items():
            logging.info(f"  {source}: {count} events")
        
        for country, count in country_counts.items():
            logging.info(f"  {country}: {count} events")
            
    except Exception as exc:
        logging.error(f"Scraper failed: {exc}")
        sys.exit(1)


DB_PATH = "anime_conventions.db"

if __name__ == "__main__":
    main()