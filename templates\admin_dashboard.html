{% extends "base.html" %}

{% block title %}Admin Dashboard - Anime Conventions{% endblock %}

{% block additional_styles %}
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 600;
            color: #1a1a1a;
        }

        .header-actions {
            display: flex;
            gap: 16px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-family: inherit;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .section {
            background: white;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .section h2 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
        }

        .conventions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .conventions-table th {
            background: #f9fafb;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            font-size: 0.9rem;
            color: #374151;
            border-bottom: 1px solid #e5e5e5;
        }

        .conventions-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 0.9rem;
        }

        .conventions-table tr:nth-child(even) {
            background: #f9fafb;
        }

        .conventions-table tr:hover {
            background: #f3f4f6;
        }

        .conventions-table tr:last-child td {
            border-bottom: none;
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 0.8rem;
            font-weight: 500;
            border-radius: 4px;
        }

        .country-badge {
            background: #dcfce7;
            color: #166534;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .empty-state p {
            margin-bottom: 16px;
        }

        .password-form {
            max-width: 400px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            font-family: inherit;
            font-size: 0.9rem;
            color: #374151;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .message {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 0.9rem;
        }

        .message.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .message.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 16px;
            }

            .header {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }

            .header-actions {
                justify-content: center;
            }

            .conventions-table {
                font-size: 0.85rem;
            }

            .conventions-table th,
            .conventions-table td {
                padding: 8px 12px;
            }
        }
{% endblock %}

{% block content %}
    <div class="container">
        <div class="header">
            <h1>Admin Dashboard</h1>
            <div class="header-actions">
                <a href="{{ url_for('index') }}" class="btn btn-secondary">View Conventions</a>
            </div>
        </div>

        <div class="section" id="pending">
            <h2>Pending Conventions</h2>
            {% if conventions %}
                <table class="conventions-table">
                    <thead>
                        <tr>
                            <th>Event Name</th>
                            <th>Dates</th>
                            <th>Location</th>
                            <th>Country</th>
                            <th>Province/State</th>
                            <th>URL</th>
                            <th>Submitted</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for convention in conventions %}
                            <tr>
                                <td>{{ convention[1] }}</td>
                                <td>{{ convention[2] }}</td>
                                <td>{{ convention[3] }}</td>
                                <td>
                                    <span class="badge country-badge">
                                        {{ "Canada" if convention[4] == "CA" else "United States" if convention[4] == "US" else convention[4] }}
                                    </span>
                                </td>
                                <td>{{ convention[5] }}</td>
                                <td>
                                    {% if convention[6] %}
                                        <a href="{{ convention[6] }}" target="_blank" rel="noopener">{{ convention[6] }}</a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ convention[7][:10] }}</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <a href="{{ url_for('edit_convention', convention_id=convention[0]) }}" 
                                           class="btn btn-warning">
                                            Edit
                                        </a>
                                        <a href="{{ url_for('approve_convention', convention_id=convention[0]) }}" 
                                           class="btn btn-primary" 
                                           onclick="return confirm('Approve this convention?')">
                                            Approve
                                        </a>
                                        <a href="{{ url_for('delete_convention', convention_id=convention[0]) }}" 
                                           class="btn btn-danger" 
                                           onclick="return confirm('Delete this convention? This action cannot be undone.')">
                                            Delete
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="empty-state">
                    <p>No pending conventions to approve.</p>
                </div>
            {% endif %}
        </div>

        <div class="section" id="approved">
            <h2>Approved User Conventions</h2>
            {% if approved_conventions %}
                <table class="conventions-table">
                    <thead>
                        <tr>
                            <th>Event Name</th>
                            <th>Dates</th>
                            <th>Location</th>
                            <th>Country</th>
                            <th>Province/State</th>
                            <th>URL</th>
                            <th>Created</th>
                            <th>Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for convention in approved_conventions %}
                            <tr>
                                <td>{{ convention[1] }}</td>
                                <td>{{ convention[2] }}</td>
                                <td>{{ convention[3] }}</td>
                                <td>
                                    <span class="badge country-badge">
                                        {{ "Canada" if convention[4] == "CA" else "United States" if convention[4] == "US" else convention[4] }}
                                    </span>
                                </td>
                                <td>{{ convention[5] }}</td>
                                <td>
                                    {% if convention[6] %}
                                        <a href="{{ convention[6] }}" target="_blank" rel="noopener">{{ convention[6] }}</a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ convention[7][:10] }}</td>
                                <td>{{ convention[8][:10] }}</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <a href="{{ url_for('edit_convention', convention_id=convention[0]) }}" 
                                           class="btn btn-warning">
                                            Edit
                                        </a>
                                        <a href="{{ url_for('delete_convention', convention_id=convention[0]) }}" 
                                           class="btn btn-danger" 
                                           onclick="return confirm('Delete this convention? This action cannot be undone.')">
                                            Delete
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="empty-state">
                    <p>No approved conventions found.</p>
                </div>
            {% endif %}
        </div>

        <div class="section">
            <h2>Change Admin Password</h2>
            <form id="passwordForm" class="password-form">
                <div class="form-group">
                    <label for="newPassword">New Password</label>
                    <input type="password" id="newPassword" name="new_password" required>
                </div>
                <button type="submit" class="btn btn-primary">Change Password</button>
            </form>
            <div id="passwordMessage" class="message" style="display: none;"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passwordForm = document.getElementById('passwordForm');
            const passwordMessage = document.getElementById('passwordMessage');

            passwordForm.addEventListener('submit', function(event) {
                event.preventDefault();
                const formData = new FormData(passwordForm);

                fetch('{{ url_for("change_password") }}', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    passwordMessage.style.display = 'block';
                    if (data.success) {
                        passwordMessage.textContent = data.message;
                        passwordMessage.className = 'message success';
                        passwordForm.reset();
                    } else {
                        passwordMessage.textContent = data.message;
                        passwordMessage.className = 'message error';
                    }
                })
                .catch(error => {
                    passwordMessage.style.display = 'block';
                    passwordMessage.textContent = 'Error changing password: ' + error.message;
                    passwordMessage.className = 'message error';
                });
            });
        });
    </script>
{% endblock %} 