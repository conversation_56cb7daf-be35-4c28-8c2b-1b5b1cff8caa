#!/usr/bin/env python3
"""
Test script to verify the scraper fix for ongoing events.
"""

from datetime import datetime, date
from scraper import first_event_date, last_event_date, is_event_current_or_future

def test_date_parsing_functions():
    """Test the new date parsing functions."""
    
    test_cases = [
        # Format: (date_string, expected_first_date, expected_last_date, description)
        ("July 11-13, 2025", date(2025, 7, 11), date(2025, 7, 13), "Multi-day event same month"),
        ("July 13, 2025", date(2025, 7, 13), date(2025, 7, 13), "Single day event"),
        ("June 30 - July 15, 2025", date(2025, 6, 30), date(2025, 7, 15), "Cross-month event"),
        ("August 8-10, 2025", date(2025, 8, 8), date(2025, 8, 10), "Multi-day event August"),
    ]
    
    print("Testing date parsing functions:")
    print("=" * 80)
    
    for date_str, expected_first, expected_last, description in test_cases:
        first_result = first_event_date(date_str)
        last_result = last_event_date(date_str)
        
        first_date = first_result.date() if first_result else None
        last_date = last_result.date() if last_result else None
        
        first_status = "✓" if first_date == expected_first else "✗"
        last_status = "✓" if last_date == expected_last else "✗"
        
        print(f"{first_status}{last_status} | {date_str:<25} | {description}")
        print(f"     First: Expected {expected_first}, Got {first_date}")
        print(f"     Last:  Expected {expected_last}, Got {last_date}")
        print()

def test_current_or_future_logic():
    """Test the is_event_current_or_future function."""
    
    # Today is July 13, 2025
    today = datetime.now().date()
    print(f"Testing is_event_current_or_future for today: {today}")
    print("=" * 80)
    
    test_cases = [
        # Format: (date_string, expected_result, description)
        ("July 11-13, 2025", True, "Event ending today (should be included)"),
        ("July 13, 2025", True, "Event happening today (should be included)"),
        ("July 13-15, 2025", True, "Event starting today (should be included)"),
        ("July 10-12, 2025", False, "Event ended yesterday (should be excluded)"),
        ("July 14-16, 2025", True, "Event starts tomorrow (should be included)"),
        ("July 11-15, 2025", True, "Multi-day event spanning today (should be included)"),
        ("June 30 - July 15, 2025", True, "Cross-month event spanning today (should be included)"),
        ("June 30 - July 12, 2025", False, "Cross-month event ended yesterday (should be excluded)"),
    ]
    
    for date_str, expected, description in test_cases:
        result = is_event_current_or_future(date_str)
        status = "✓ PASS" if result == expected else "✗ FAIL"
        print(f"{status} | {date_str:<25} | {description}")
        if result != expected:
            print(f"      Expected: {expected}, Got: {result}")
    
    print()

def test_scraper_with_ai_kon():
    """Test specifically with Ai-Kon 2025 which should be happening today."""
    
    print("Testing Ai-Kon 2025 specifically:")
    print("=" * 80)
    
    ai_kon_dates = "July 11-13, 2025"
    
    first_date = first_event_date(ai_kon_dates)
    last_date = last_event_date(ai_kon_dates)
    is_current = is_event_current_or_future(ai_kon_dates)
    
    print(f"Ai-Kon dates: {ai_kon_dates}")
    print(f"First date: {first_date.date() if first_date else None}")
    print(f"Last date: {last_date.date() if last_date else None}")
    print(f"Is current or future: {is_current}")
    print(f"Today: {datetime.now().date()}")
    
    if is_current:
        print("✓ Ai-Kon 2025 would be included by the scraper")
    else:
        print("✗ Ai-Kon 2025 would be excluded by the scraper")

if __name__ == "__main__":
    print("Scraper Fix Test Script")
    print("=" * 80)
    
    test_date_parsing_functions()
    test_current_or_future_logic()
    test_scraper_with_ai_kon()
