<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Primary Meta Tags -->
    <title>{% block title %}Anime Conventions{% endblock %}</title>
    <meta name="title" content="{% block meta_title %}Anime Conventions & Events - Find Anime Cons Near You{% endblock %}">
    <meta name="description" content="{% block meta_description %}Discover upcoming anime conventions, cosplay events, and anime gatherings across Canada and the United States. Find anime cons by location, dates, and more.{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}anime conventions, anime cons, cosplay events, anime gatherings, anime festivals, otaku events, manga conventions, anime conventions 2024, anime conventions 2025, anime events near me{% endblock %}">
    <meta name="author" content="Anime Conventions Directory">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:title" content="{% block og_title %}Anime Conventions & Events - Find Anime Cons Near You{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Discover upcoming anime conventions, cosplay events, and anime gatherings across Canada and the United States. Find anime cons by location, dates, and more.{% endblock %}">
    <meta property="og:image" content="{% block og_image %}{{ url_for('static', filename='images/anime-conventions-og.jpg', _external=True) }}{% endblock %}">
    <meta property="og:site_name" content="Anime Conventions Directory">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ request.url }}">
    <meta property="twitter:title" content="{% block twitter_title %}Anime Conventions & Events - Find Anime Cons Near You{% endblock %}">
    <meta property="twitter:description" content="{% block twitter_description %}Discover upcoming anime conventions, cosplay events, and anime gatherings across Canada and the United States. Find anime cons by location, dates, and more.{% endblock %}">
    <meta property="twitter:image" content="{% block twitter_image %}{{ url_for('static', filename='images/anime-conventions-twitter.jpg', _external=True) }}{% endblock %}">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="{{ request.url }}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon-16x16.png') }}">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Anime Conventions Directory",
        "description": "Comprehensive directory of anime conventions and events across Canada and the United States",
        "url": "{{ request.url_root }}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.url_root }}?search={search_term_string}",
            "query-input": "required name=search_term_string"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Anime Conventions Directory",
            "description": "Your go-to resource for finding anime conventions and events"
        }
    }
    </script>
    
    <!-- Additional Structured Data for Events -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ItemList",
        "name": "Anime Conventions and Events",
        "description": "List of upcoming anime conventions, cosplay events, and anime gatherings",
        "itemListElement": []
    }
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #fafafa;
            color: #1a1a1a;
            line-height: 1.5;
        }

        .admin-bar {
            background: #1f2937;
            color: white;
            padding: 8px 0;
            font-size: 0.9rem;
        }

        .admin-bar-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-bar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .admin-badge {
            background: #dc2626;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .admin-bar a {
            color: #d1d5db;
            text-decoration: none;
            padding: 4px 8px;
            border-radius: 4px;
            transition: color 0.2s;
        }

        .admin-bar a:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .admin-bar .btn {
            background: #dc2626;
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            text-decoration: none;
        }

        .admin-bar .btn:hover {
            background: #b91c1c;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        {% block additional_styles %}{% endblock %}
    </style>
</head>
<body>
    {% if session.get('admin_logged_in') %}
        <div class="admin-bar">
            <div class="admin-bar-content">
                <div class="admin-bar-left">
                    <span class="admin-badge">ADMIN</span>
                    <a href="{{ url_for('index') }}">View Conventions</a>
                    <a href="{{ url_for('admin_dashboard') }}">Dashboard</a>
                </div>
                <div class="admin-bar-right">
                    <a href="{{ url_for('admin_logout') }}" class="btn">Logout</a>
                </div>
            </div>
        </div>
    {% endif %}

    {% block content %}{% endblock %}
</body>
</html> 